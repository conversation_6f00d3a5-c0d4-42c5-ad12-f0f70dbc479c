import React from "react";
import { View, StyleSheet, ScrollView } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Text } from "../../components/ui/text";
import { useUser } from "@clerk/clerk-expo";
import { HomeHeader } from "@/components/home/<USER>";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  <PERSON>,
  Du<PERSON>bell,
  User,
  Brain,
  CalendarHeart,
  MessageCircle,
} from "lucide-react-native";
import HealthSummary from "@/components/home/<USER>";
import { TouchableOpacity } from "react-native";
import { useRouter } from "expo-router";
import { Separator } from "@/components/ui/separator";
import FitnessSummary from "@/components/home/<USER>";
import SleepSummary from "@/components/home/<USER>";
import MoodSummary from "@/components/home/<USER>";
import LogMoodSheet from "@/components/mood/log-mood-sheet";

export default function DashboardScreen() {
  const router = useRouter();
  const [isMoodSheetOpen, setIsMoodSheetOpen] = React.useState(false);

  return (
    <SafeAreaView
      edges={["left", "right", "top"]}
      style={{
        flex: 1,
        padding: 0,
        margin: 0,
      }}
    >
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.container}>
          <HomeHeader />
          <HealthSummary />
          <MoodSummary onLogMoodPress={() => setIsMoodSheetOpen(true)} />
          <SleepSummary />
          <FitnessSummary />
        </View>
        <TouchableOpacity
          className="bg-primary"
          style={styles.fab}
          onPress={() => router.push("/chat")}
        >
          <MessageCircle size={24} color="white" />
        </TouchableOpacity>
      </ScrollView>
      <LogMoodSheet
        isOpen={isMoodSheetOpen}
        onClose={() => setIsMoodSheetOpen(false)}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  scrollView: {
    flex: 1,
  },
  container: {
    padding: 20,
    gap: 24,
  },
  fab: {
    position: "absolute",
    width: 56,
    height: 56,
    alignItems: "center",
    justifyContent: "center",
    right: 20,
    bottom: 20,
    borderRadius: 28,
    elevation: 8, // For Android shadow
    shadowColor: "#000", // For iOS shadow
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
  },
});
