import { View } from "react-native";
import React from "react";
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import { <PERSON>, CardHeader, CardContent, CardFooter } from "../ui/card";
import { Text } from "../ui/text";
import { Button } from "../ui/button";
import { Progress } from "../ui/progress";
import {
  HeartPlus,
  Coffee,
  Utensils,
  Moon,
  AlertCircle,
  ForkKnife,
  Bot,
} from "lucide-react-native";
import { SkeletonLayout } from "../ui/Skeleton";
import { Alert, AlertDescription, AlertTitle } from "../ui/alert";
import { router } from "expo-router";

const HealthSummary = () => {
  const healthPlan = useQuery(api.healthPlans.getActiveHealthPlan);
  const dailyConsumedCalories = useQuery(api.meals.getTotalCaloriesToday);

  if (healthPlan === undefined || dailyConsumedCalories === undefined)
    return <SkeletonLayout showGrid />;

  if (healthPlan === null) {
    return (
      <Card className="flex flex-col items-center justify-center p-6">
        <HeartPlus color={"#6b7280"} size={48} />
        <Text className="font-bold text-lg mt-4">No Health Plan Found</Text>
        <Text className="text-sm text-muted-foreground text-center mt-2">
          It looks like you don't have an active health plan. Create one to
          track your daily calories and health goals!
        </Text>
        <Button
          onPress={() => router.push("/health/new-plan")}
          className="w-full mt-4"
          variant="default"
        >
          <Text>Create Health Plan</Text>
        </Button>
      </Card>
    );
  }

  return (
    <View>
      <CardHeader className="flex-row px-0 py-4 items-center justify-between">
        <View className="flex flex-row items-center gap-4 flex-1">
          <Button size={"icon"} variant={"default"} className="h-12 w-12">
            <HeartPlus color={"white"} size={20} />
          </Button>
          <View className="flex-1">
            <Text className="font-bold text-xl capitalize tracking-tight">
              My {healthPlan.type} Plan
            </Text>
            <Text className="text-sm text-muted-foreground mt-1">
              Active health plan
            </Text>
          </View>
          <Button
            variant="ghost"
            size="sm"
            onPress={() => router.push("/health/edit-plan")}
            className="px-3 py-1"
          >
            <Text className="text-xs text-primary">Edit Plan</Text>
          </Button>
        </View>
      </CardHeader>

      <View className="px-0 pb-4">
        <View className=" border border-primary/20 rounded-lg p-4">
          <View className="flex-row items-center justify-between">
            <View>
              <Text className="text-sm text-muted-foreground">
                Daily Target
              </Text>
              <Text className="text-3xl font-bold text-primary">
                {healthPlan.dailyCalories.breakfast +
                  healthPlan.dailyCalories.lunch +
                  healthPlan.dailyCalories.dinner +
                  healthPlan.dailyCalories.snacks}
              </Text>
              <Text className="text-sm font-medium text-muted-foreground">
                calories per day
              </Text>
            </View>
            <View className="items-end">
              <Text className="text-sm text-muted-foreground">
                Progress Today
              </Text>
              <Text className="text-lg font-bold text-foreground">
                {dailyConsumedCalories.breakfast +
                  dailyConsumedCalories.lunch +
                  dailyConsumedCalories.dinner +
                  dailyConsumedCalories.snacks}
              </Text>
              <Text className="text-xs text-muted-foreground">
                {Math.round(
                  ((dailyConsumedCalories.breakfast +
                    dailyConsumedCalories.lunch +
                    dailyConsumedCalories.dinner +
                    dailyConsumedCalories.snacks) /
                    (healthPlan.dailyCalories.breakfast +
                      healthPlan.dailyCalories.lunch +
                      healthPlan.dailyCalories.dinner +
                      healthPlan.dailyCalories.snacks)) *
                    100
                )}
                % of target
              </Text>
            </View>
          </View>
        </View>
      </View>

      <CardContent className="px-0">
        <View className="flex-row items-center gap-3">
          {["breakfast", "lunch", "dinner"].map((mealType) => {
            const consumed =
              dailyConsumedCalories[
                mealType as keyof typeof dailyConsumedCalories
              ];
            const target =
              healthPlan.dailyCalories[
                mealType as keyof typeof healthPlan.dailyCalories
              ];
            const progressValue = target > 0 ? (consumed / target) * 100 : 0;

            return (
              <View
                key={mealType}
                className="p-4 shadow-sm drop-shadow-md border border-border rounded-lg flex-1 flex-col items-center justify-center bg-card"
              >
                <View className="mb-3">
                  {mealType === "breakfast" && (
                    <Coffee color={"#007bff"} size={24} />
                  )}
                  {mealType === "lunch" && (
                    <Utensils color={"#e67e22"} size={24} />
                  )}
                  {mealType === "dinner" && (
                    <Moon color={"#27ae60"} size={24} />
                  )}
                </View>

                <Text className="text-sm font-semibold capitalize mb-2">
                  {mealType}
                </Text>

                <Text className="text-base font-bold text-foreground mb-3">
                  {consumed} / {target}
                </Text>

                <View className="w-full">
                  <Progress
                    value={Math.min(progressValue, 100)}
                    className="h-2"
                    indicatorClassName={
                      progressValue > 100
                        ? "bg-destructive"
                        : progressValue >= 80
                          ? "bg-primary"
                          : "bg-orange-500"
                    }
                  />
                </View>

                <Text className="text-xs text-muted-foreground mt-2">
                  {Math.round(progressValue)}%
                </Text>
              </View>
            );
          })}
        </View>

        {(() => {
          const exceededMeals: string[] = [];
          if (
            dailyConsumedCalories.breakfast > healthPlan.dailyCalories.breakfast
          ) {
            exceededMeals.push("Breakfast");
          }
          if (dailyConsumedCalories.lunch > healthPlan.dailyCalories.lunch) {
            exceededMeals.push("Lunch");
          }
          if (dailyConsumedCalories.dinner > healthPlan.dailyCalories.dinner) {
            exceededMeals.push("Dinner");
          }

          if (exceededMeals.length > 0) {
            return (
              <Alert icon={AlertCircle} variant="destructive" className="mt-4">
                <AlertTitle className="font-medium">
                  Calories Exceeded!
                </AlertTitle>
                <AlertDescription className="text-muted-foreground">
                  You consumed more calories than your target for{" "}
                  {exceededMeals.join(", and ")}.
                </AlertDescription>
              </Alert>
            );
          }
          return null;
        })()}
      </CardContent>
      <CardFooter className="px-0 pt-6">
        <View className="flex-row w-full gap-4">
          <Button variant={"outline"} className="flex-1 flex-row gap-3 h-12">
            <Bot color={"#6b7280"} size={18} />
            <Text className="text-muted-foreground">Call Ray</Text>
          </Button>
          <Button
            onPress={() => router.push("/health/log-meal")}
            variant={"default"}
            className="flex-1 flex-row gap-3 h-12 bg-primary"
          >
            <ForkKnife color={"white"} size={18} />
            <Text className="text-white font-semibold">Add Meal</Text>
          </Button>
        </View>
      </CardFooter>
    </View>
  );
};

export default HealthSummary;
