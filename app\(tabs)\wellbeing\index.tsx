import React from "react";
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Dimensions,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { useRouter } from "expo-router";
import { useQuery } from "convex/react";
import { api } from "@/convex/_generated/api";
import {
  Moon,
  Heart,
  Brain,
  TrendingUp,
  Target,
  Smile,
  Activity,
  ChevronRight,
} from "lucide-react-native";
import { LinearGradient } from "expo-linear-gradient";
import { BarChart } from "react-native-gifted-charts";
import { Header } from "@/components/ui/Header";

const { width } = Dimensions.get("window");

export default function WellbeingDashboard() {
  const router = useRouter();

  // Get recent data
  const recentMoodEntries = useQuery(api.mood.getMoodEntries, {
    startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
      .toISOString()
      .split("T")[0],
    endDate: new Date().toISOString().split("T")[0],
  });

  const sleepSummary = useQuery(api.sleep.getLast7DaysSleepSummary);
  const lastSleepEntry = useQuery(api.sleep.getLastSleepEntry);
  const currentMood = useQuery(api.mood.getCurrentMood);

  const averageSleepDuration = useQuery(api.sleep.getAverageSleepDuration, {
    startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
      .toISOString()
      .split("T")[0],
    endDate: new Date().toISOString().split("T")[0],
  });

  // Get enhanced wellbeing data
  const wellbeingScore = useQuery(api.wellbeing.getWellbeingScore);
  const currentWellbeingEntry = useQuery(
    api.wellbeingEntry.getCurrentWellbeingEntry
  );
  const activeGoals = useQuery(api.wellbeingGoals.getActiveWellbeingGoals);
  const streakSummary = useQuery(api.wellbeingStreaks.getStreakSummary);
  const meditationSessions = useQuery(api.meditation.getMeditationSessions, {
    startDate: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
      .toISOString()
      .split("T")[0],
    endDate: new Date().toISOString().split("T")[0],
  });

  // Use the enhanced wellbeing score from backend
  const currentWellbeingScore = wellbeingScore?.score || 70;

  const getMoodColor = (mood: string) => {
    const colors: Record<string, string> = {
      happy: "#22c55e",
      excited: "#f59e0b",
      calm: "#06b6d4",
      neutral: "#6b7280",
      tired: "#8b5cf6",
      stressed: "#ef4444",
      anxious: "#f97316",
      sad: "#3b82f6",
      angry: "#dc2626",
      other: "#64748b",
    };
    return colors[mood] || "#6b7280";
  };

  const QuickActionCard = ({
    title,
    subtitle,
    icon: Icon,
    color,
    onPress,
  }: {
    title: string;
    subtitle: string;
    icon: any;
    color: string;
    onPress: () => void;
  }) => (
    <TouchableOpacity
      onPress={onPress}
      className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100 flex-1 mx-1"
    >
      <View className="flex-row items-center justify-between mb-3">
        <View
          className={`w-10 h-10 rounded-xl items-center justify-center`}
          style={{ backgroundColor: `${color}20` }}
        >
          <Icon size={20} color={color} />
        </View>
        <ChevronRight size={16} color="#9ca3af" />
      </View>
      <Text className="text-gray-900 font-semibold text-base mb-1">
        {title}
      </Text>
      <Text className="text-gray-500 text-sm">{subtitle}</Text>
    </TouchableOpacity>
  );

  const MetricCard = ({
    title,
    value,
    unit,
    trend,
    icon: Icon,
    color,
  }: {
    title: string;
    value: string | number;
    unit?: string;
    trend?: number;
    icon: any;
    color: string;
  }) => (
    <View className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100 flex-1 mx-1">
      <View className="flex-row items-center justify-between mb-3">
        <View
          className={`w-8 h-8 rounded-lg items-center justify-center`}
          style={{ backgroundColor: `${color}20` }}
        >
          <Icon size={16} color={color} />
        </View>
        {trend !== undefined && (
          <View className="flex-row items-center">
            <TrendingUp size={12} color={trend > 0 ? "#22c55e" : "#ef4444"} />
            <Text
              className={`text-xs ml-1 ${trend > 0 ? "text-green-500" : "text-red-500"}`}
            >
              {trend > 0 ? "+" : ""}
              {trend}%
            </Text>
          </View>
        )}
      </View>
      <Text className="text-2xl font-bold text-gray-900 mb-1">
        {value}
        {unit && <Text className="text-lg text-gray-500">{unit}</Text>}
      </Text>
      <Text className="text-gray-500 text-sm">{title}</Text>
    </View>
  );

  return (
    <SafeAreaView className="flex-1 bg-gray-50">
      <ScrollView showsVerticalScrollIndicator={false} className="flex-1">
        <Header title="Wellbeing" />

        {/* Wellbeing Score Card */}
        <View className="mx-6 mb-6">
          <LinearGradient
            colors={["#667eea", "#764ba2"]}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
            className="rounded-3xl p-6"
          >
            <View className="flex-row items-center justify-between">
              <View className="flex-1">
                <Text className="text-white text-lg font-semibold mb-2">
                  Wellbeing Score
                </Text>
                <Text className="text-white text-4xl font-bold mb-1">
                  {currentWellbeingScore}
                </Text>
                <Text className="text-white/80 text-sm">
                  {currentWellbeingScore >= 80
                    ? "Excellent"
                    : currentWellbeingScore >= 60
                      ? "Good"
                      : "Needs Attention"}
                </Text>
              </View>
              <View className="w-20 h-20 rounded-full bg-white/20 items-center justify-center">
                <Activity size={32} color="white" />
              </View>
            </View>
          </LinearGradient>
        </View>

        {/* Quick Metrics */}
        <View className="px-6 mb-6">
          <Text className="text-xl font-semibold text-gray-900 mb-4">
            Today's Overview
          </Text>
          <View className="flex-row mb-3">
            <MetricCard
              title="Sleep Quality"
              value={lastSleepEntry?.qualityRating || 0}
              unit="/5"
              trend={5}
              icon={Moon}
              color="#8b5cf6"
            />
            <MetricCard
              title="Current Mood"
              value={
                currentMood?.mood
                  ? currentMood.mood.charAt(0).toUpperCase() +
                    currentMood.mood.slice(1)
                  : "Not set"
              }
              icon={Smile}
              color={
                currentMood?.mood ? getMoodColor(currentMood.mood) : "#6b7280"
              }
            />
          </View>
          <View className="flex-row">
            <MetricCard
              title="Stress Level"
              value={currentWellbeingEntry?.stress || "Not set"}
              unit={currentWellbeingEntry?.stress ? "/10" : ""}
              icon={Heart}
              color="#ef4444"
            />
            <MetricCard
              title="Energy Level"
              value={currentWellbeingEntry?.energy || "Not set"}
              unit={currentWellbeingEntry?.energy ? "/10" : ""}
              icon={Activity}
              color="#22c55e"
            />
          </View>
        </View>

        {/* Active Goals */}
        {activeGoals && activeGoals.length > 0 && (
          <View className="px-6 mb-6">
            <Text className="text-xl font-semibold text-gray-900 mb-4">
              Active Goals
            </Text>
            <View className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100">
              {activeGoals.slice(0, 2).map((goal, index) => (
                <View
                  key={goal._id}
                  className={`${index > 0 ? "mt-4 pt-4 border-t border-gray-100" : ""}`}
                >
                  <View className="flex-row items-center justify-between mb-2">
                    <Text className="font-semibold text-gray-900">
                      {goal.title}
                    </Text>
                    <Text className="text-sm text-gray-500">
                      {Math.round(
                        ((goal.currentValue || 0) / goal.targetValue) * 100
                      )}
                      %
                    </Text>
                  </View>
                  <View className="bg-gray-200 rounded-full h-2 mb-2">
                    <View
                      className="bg-blue-500 h-2 rounded-full"
                      style={{
                        width: `${Math.min(100, ((goal.currentValue || 0) / goal.targetValue) * 100)}%`,
                      }}
                    />
                  </View>
                  <Text className="text-xs text-gray-600">
                    {goal.currentValue || 0} / {goal.targetValue} {goal.unit}
                  </Text>
                </View>
              ))}
            </View>
          </View>
        )}

        {/* Streaks */}
        {streakSummary && streakSummary.totalActiveStreaks > 0 && (
          <View className="px-6 mb-6">
            <Text className="text-xl font-semibold text-gray-900 mb-4">
              Current Streaks
            </Text>
            <View className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100">
              <View className="flex-row justify-between items-center">
                <View className="items-center flex-1">
                  <Text className="text-2xl font-bold text-orange-500">
                    {streakSummary.totalActiveStreaks}
                  </Text>
                  <Text className="text-gray-600 text-sm">Active Streaks</Text>
                </View>
                <View className="items-center flex-1">
                  <Text className="text-2xl font-bold text-green-600">
                    {streakSummary.longestCurrentStreak}
                  </Text>
                  <Text className="text-gray-600 text-sm">Best Current</Text>
                </View>
                <View className="items-center flex-1">
                  <Text className="text-2xl font-bold text-blue-600">
                    {streakSummary.totalLongestStreak}
                  </Text>
                  <Text className="text-gray-600 text-sm">All-Time Best</Text>
                </View>
              </View>
            </View>
          </View>
        )}

        {/* Sleep Chart */}
        {sleepSummary && sleepSummary.length > 0 && (
          <View className="px-6 mb-6">
            <View className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100">
              <Text className="text-lg font-semibold text-gray-900 mb-4">
                Sleep Patterns
              </Text>
              <BarChart
                data={sleepSummary.map((item) => ({
                  value: item.value,
                  label: item.label,
                  frontColor: "#8b5cf6",
                }))}
                width={width - 80}
                height={180}
                barWidth={32}
                spacing={20}
                roundedTop
                roundedBottom
                hideRules
                xAxisThickness={0}
                yAxisThickness={0}
                yAxisTextStyle={{ color: "#9ca3af", fontSize: 12 }}
                xAxisLabelTextStyle={{ color: "#9ca3af", fontSize: 12 }}
                noOfSections={4}
                maxValue={10}
              />
            </View>
          </View>
        )}

        {/* Quick Actions */}
        <View className="px-6 mb-6">
          <Text className="text-xl font-semibold text-gray-900 mb-4">
            Quick Actions
          </Text>
          <View className="flex-row mb-3">
            <QuickActionCard
              title="Log Sleep"
              subtitle="Track last night"
              icon={Moon}
              color="#8b5cf6"
              onPress={() => router.push("/wellbeing/sleep")}
            />
            <QuickActionCard
              title="Log Mood"
              subtitle="How are you feeling?"
              icon={Heart}
              color="#f59e0b"
              onPress={() => router.push("/wellbeing/mood")}
            />
          </View>
          <View className="flex-row mb-3">
            <QuickActionCard
              title="Meditate"
              subtitle="Find your calm"
              icon={Brain}
              color="#06b6d4"
              onPress={() => router.push("/wellbeing/meditation")}
            />
            <QuickActionCard
              title="Wellbeing Check"
              subtitle="How are you feeling?"
              icon={Heart}
              color="#8b5cf6"
              onPress={() => router.push("/wellbeing/wellbeing-check")}
            />
          </View>
          <View className="flex-row">
            <QuickActionCard
              title="AI Insights"
              subtitle="Personalized tips"
              icon={TrendingUp}
              color="#22c55e"
              onPress={() => router.push("/wellbeing/insights")}
            />
            <QuickActionCard
              title="View Goals"
              subtitle="Track progress"
              icon={Target}
              color="#f59e0b"
              onPress={() => router.push("/wellbeing/goals")}
            />
          </View>
        </View>

        {/* Weekly Summary */}
        <View className="px-6 mb-8">
          <View className="bg-white rounded-2xl p-4 shadow-sm border border-gray-100">
            <Text className="text-lg font-semibold text-gray-900 mb-4">
              This Week
            </Text>
            <View className="space-y-3">
              <View className="flex-row items-center justify-between">
                <Text className="text-gray-600">Average Sleep</Text>
                <Text className="font-semibold text-gray-900">
                  {averageSleepDuration?.toFixed(1) || "0"}h
                </Text>
              </View>
              <View className="flex-row items-center justify-between">
                <Text className="text-gray-600">Mood Entries</Text>
                <Text className="font-semibold text-gray-900">
                  {recentMoodEntries?.length || 0}
                </Text>
              </View>
              <View className="flex-row items-center justify-between">
                <Text className="text-gray-600">Meditation Sessions</Text>
                <Text className="font-semibold text-gray-900">
                  {meditationSessions?.length || 0}
                </Text>
              </View>
              <View className="flex-row items-center justify-between">
                <Text className="text-gray-600">Wellbeing Score</Text>
                <Text className="font-semibold text-gray-900">
                  {currentWellbeingScore}/100
                </Text>
              </View>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}
